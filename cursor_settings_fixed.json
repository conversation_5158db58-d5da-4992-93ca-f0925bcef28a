{
    "window.commandCenter": true,
    
    // 🚀 无人值守全自动化配置
    "cursor.automaticSuggestions": true,
    "cursor.autoAcceptChanges": true,
    "cursor.confirmBeforeApplyingChanges": false,
    "cursor.showConfirmationDialogs": false,
    
    // AI自动化设置
    "cursor.chat.autoAcceptSuggestions": true,
    "cursor.chat.confirmBeforeExecuting": false,
    "cursor.chat.enableAutomaticExecution": true,
    "cursor.chat.autoApplyCodeChanges": true,
    
    // 编辑器自动化设置
    "editor.acceptSuggestionOnCommitCharacter": true,
    "editor.acceptSuggestionOnEnter": "on",
    "editor.quickSuggestions": {
        "other": true,
        "comments": true,
        "strings": true
    },
    "editor.suggestOnTriggerCharacters": true,
    "editor.wordBasedSuggestions": "allDocuments",
    
    // 取消所有确认对话框
    "git.confirmSync": false,
    "git.enableSmartCommit": true,
    "git.autofetch": true,
    "git.autoStash": true,
    
    // 文件操作自动化
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    
    // 终端自动化
    "terminal.integrated.confirmOnExit": "never",
    "terminal.integrated.confirmOnKill": "never",
    
    // 调试自动化
    "debug.console.acceptSuggestionOnEnter": "on",
    "debug.confirmOnExit": "never",
    
    // 扩展自动化
    "extensions.autoUpdate": true,
    "extensions.autoCheckUpdates": true,
    "extensions.ignoreRecommendations": false,
    
    // 工作区自动化
    "workbench.settings.enableNaturalLanguageSearch": true,
    "workbench.commandPalette.preserveInput": true,
    "workbench.quickOpen.preserveInput": true,
    
    // 通知设置
    "workbench.reduceMotion": "off",
    "notifications.showErrors": false,
    "notifications.showWarnings": false,
    "notifications.showInfos": false,
    
    // 性能优化
    "editor.minimap.enabled": false,
    "editor.scrollbar.vertical": "hidden",
    "editor.scrollbar.horizontal": "hidden",
    "editor.overviewRulerBorder": false,
    "editor.hideCursorInOverviewRuler": true,
    
    // 自动完成和智能感知
    "typescript.suggest.autoImports": true,
    "typescript.updateImportsOnFileMove.enabled": "always",
    "javascript.suggest.autoImports": true,
    "javascript.updateImportsOnFileMove.enabled": "always",
    
    // Python自动化
    "python.linting.enabled": true,
    "python.linting.lintOnSave": true,
    "python.formatting.provider": "autopep8",
    "python.formatting.autopep8Args": ["--aggressive", "--aggressive"],
    
    // 项目管理自动化
    "npm.enableRunFromFolder": true,
    "npm.enableScriptExplorer": true,
    "npm.scriptExplorerAction": "run",
    
    // 实验性功能
    "workbench.experimental.settingsProfiles.enabled": true,
    "editor.experimental.asyncTokenization": true,
    
    // Cursor特定的无人值守配置
    "cursor.ai.autoExecuteCommands": true,
    "cursor.ai.skipConfirmation": true,
    "cursor.ai.enableBackgroundProcessing": true,
    "cursor.ai.autoApplyAllSuggestions": true,
    "cursor.ai.silentMode": true,
    "cursor.workspace.autoManagement": true,
    "cursor.files.autoSaveAndCommit": true,
    "cursor.terminal.autoExecute": true,
    "cursor.debug.autoStart": true,
    "cursor.extensions.autoInstall": true,

    // 🔧 修复Monaco Editor加载问题的代理设置
    "http.proxy": "http://127.0.0.1:10808",
    "http.proxyStrictSSL": false,
    "http.proxySupport": "override",
    "http.noProxy": [
        "localhost",
        "127.0.0.1",
        "*.microsoft.com",
        "*.vscode-cdn.net",
        "*.jsdelivr.net",
        "*.unpkg.com",
        "*.cdnjs.cloudflare.com",
        "*.monaco-editor.net",
        "*.vscode.dev"
    ],
    
    // Monaco Editor 和 WebView 修复设置
    "editor.wordWrap": "on",
    "editor.fontSize": 14,
    "editor.fontFamily": "Consolas, 'Courier New', monospace",
    "webview.experimental.useIframes": true,
    "extensions.webWorker": true,
    "security.workspace.trust.enabled": false,
    
    // Augment 特定修复设置
    "augment.enableTelemetry": false,
    "augment.autoUpdate": true,
    "augment.experimental.webview": true,
    "augment.monaco.loadTimeout": 30000,
    "augment.webview.allowScripts": true,
    
    // 网络和安全设置
    "security.allowedUNCHosts": ["*"],
    "remote.downloadExtensionsLocally": true,
    "extensions.autoCheckUpdates": true,
    "extensions.ignoreRecommendations": false
}
