# Cursor Augment Monaco Editor 修复脚本
Write-Host "开始修复 Cursor Augment Monaco Editor..." -ForegroundColor Green

# 1. 清理缓存
Write-Host "清理 Cursor 缓存..." -ForegroundColor Yellow
$cachePaths = @(
    "$env:APPDATA\Cursor\CachedExtensions",
    "$env:APPDATA\Cursor\logs", 
    "$env:APPDATA\Cursor\CachedData"
)

foreach ($path in $cachePaths) {
    if (Test-Path $path) {
        Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "已清理: $path" -ForegroundColor Green
    }
}

# 2. 刷新DNS
Write-Host "刷新DNS缓存..." -ForegroundColor Yellow
ipconfig /flushdns | Out-Null
Write-Host "DNS缓存已刷新" -ForegroundColor Green

# 3. 检查代理
Write-Host "检查代理连接..." -ForegroundColor Yellow
$proxyTest = Test-NetConnection -ComputerName "127.0.0.1" -Port 10808 -WarningAction SilentlyContinue
if ($proxyTest.TcpTestSucceeded) {
    Write-Host "代理连接正常" -ForegroundColor Green
} else {
    Write-Host "代理连接失败，请检查v2rayN" -ForegroundColor Red
}

# 4. 重启Cursor
Write-Host "重启Cursor..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -like "*cursor*"} | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 3

Write-Host "修复完成！请重新启动Cursor并检查Augment面板" -ForegroundColor Green
