# 🔧 Cursor Augment Monaco Editor 修复脚本
Write-Host "🔧 开始修复 Cursor Augment Monaco Editor 加载问题..." -ForegroundColor Green

# 1. 清理 Cursor 缓存
Write-Host "📁 清理 Cursor 缓存..." -ForegroundColor Yellow
$cursorCachePaths = @(
    "$env:APPDATA\Cursor\CachedExtensions",
    "$env:APPDATA\Cursor\logs",
    "$env:APPDATA\Cursor\CachedData",
    "$env:APPDATA\Cursor\User\workspaceStorage",
    "$env:LOCALAPPDATA\cursor\User\CachedExtensions"
)

foreach ($path in $cursorCachePaths) {
    if (Test-Path $path) {
        try {
            Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "✅ 已清理: $path" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ 无法清理: $path - $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

# 2. 重置网络设置
Write-Host "🌐 重置网络设置..." -ForegroundColor Yellow
try {
    # 刷新 DNS
    ipconfig /flushdns | Out-Null
    Write-Host "✅ DNS 缓存已刷新" -ForegroundColor Green
    
    # 重置网络适配器
    netsh winsock reset | Out-Null
    Write-Host "✅ Winsock 已重置" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 网络重置失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 3. 检查代理连接
Write-Host "🔍 检查代理连接..." -ForegroundColor Yellow
try {
    $proxyTest = Test-NetConnection -ComputerName "127.0.0.1" -Port 10808 -WarningAction SilentlyContinue
    if ($proxyTest.TcpTestSucceeded) {
        Write-Host "✅ 代理服务器 127.0.0.1:10808 连接正常" -ForegroundColor Green
    } else {
        Write-Host "❌ 代理服务器 127.0.0.1:10808 连接失败" -ForegroundColor Red
        Write-Host "💡 请检查 v2rayN 或其他代理软件是否正常运行" -ForegroundColor Cyan
    }
} catch {
    Write-Host "⚠️ 代理连接检查失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 4. 测试关键域名解析
Write-Host "🌍 测试关键域名解析..." -ForegroundColor Yellow
$testDomains = @(
    "microsoft.com",
    "vscode-cdn.net", 
    "jsdelivr.net",
    "unpkg.com"
)

foreach ($domain in $testDomains) {
    try {
        $result = Resolve-DnsName $domain -ErrorAction Stop
        Write-Host "✅ $domain 解析正常" -ForegroundColor Green
    } catch {
        Write-Host "❌ $domain 解析失败" -ForegroundColor Red
    }
}

# 5. 创建 Augment 修复配置
Write-Host "⚙️ 创建 Augment 修复配置..." -ForegroundColor Yellow
$augmentFixConfig = @{
    "augment.monaco.loadTimeout" = 60000
    "augment.webview.allowScripts" = $true
    "augment.experimental.webview" = $true
    "augment.network.timeout" = 30000
    "augment.cache.enabled" = $false
    "webview.experimental.useIframes" = $true
    "security.workspace.trust.enabled" = $false
    "extensions.webWorker" = $true
}

$settingsPath = "$env:APPDATA\Cursor\User\settings.json"
if (Test-Path $settingsPath) {
    try {
        $settings = Get-Content $settingsPath -Raw | ConvertFrom-Json
        
        # 添加修复配置
        foreach ($key in $augmentFixConfig.Keys) {
            $settings | Add-Member -Name $key -Value $augmentFixConfig[$key] -Force
        }
        
        # 保存配置
        $settings | ConvertTo-Json -Depth 10 | Set-Content $settingsPath -Encoding UTF8
        Write-Host "✅ Augment 修复配置已应用" -ForegroundColor Green
    } catch {
        Write-Host "❌ 配置应用失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 6. 重启 Cursor 进程
Write-Host "🔄 重启 Cursor 进程..." -ForegroundColor Yellow
try {
    $cursorProcesses = Get-Process | Where-Object {$_.ProcessName -like "*cursor*"}
    if ($cursorProcesses) {
        $cursorProcesses | Stop-Process -Force
        Write-Host "✅ Cursor 进程已终止" -ForegroundColor Green
        Start-Sleep -Seconds 3
        
        # 尝试重新启动 Cursor
        $cursorPath = "$env:LOCALAPPDATA\Programs\cursor\Cursor.exe"
        if (Test-Path $cursorPath) {
            Start-Process $cursorPath
            Write-Host "✅ Cursor 已重新启动" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 请手动重新启动 Cursor" -ForegroundColor Yellow
        }
    } else {
        Write-Host "ℹ️ 未找到运行中的 Cursor 进程" -ForegroundColor Cyan
    }
} catch {
    Write-Host "⚠️ 进程重启失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 修复脚本执行完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 接下来的步骤:" -ForegroundColor Cyan
Write-Host "1. 等待 Cursor 完全启动" -ForegroundColor White
Write-Host "2. 打开 Augment 面板检查是否正常加载" -ForegroundColor White
Write-Host "3. 如果仍有问题，请尝试禁用代理后重试" -ForegroundColor White
Write-Host "4. 检查开发者控制台 (Ctrl+Shift+I) 查看详细错误信息" -ForegroundColor White
Write-Host ""
Write-Host "💡 如果问题持续，可能需要:" -ForegroundColor Yellow
Write-Host "   - 重新安装 Augment 扩展" -ForegroundColor White
Write-Host "   - 临时禁用代理设置" -ForegroundColor White
Write-Host "   - 检查防火墙设置" -ForegroundColor White
